#!/usr/bin/env python3
"""
Test runner script for Voice Gateway service.

This script provides convenient commands to run different test suites.
"""

import subprocess
import sys
import argparse
from pathlib import Path


def run_command(cmd: list[str]) -> int:
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode


def run_all_tests():
    """Run all tests."""
    return run_command(["poetry", "run", "pytest", "tests/", "-v", "--tb=short"])


def run_unit_tests():
    """Run unit tests (excluding integration tests)."""
    return run_command([
        "poetry", "run", "pytest", 
        "tests/test_config.py",
        "tests/test_security.py", 
        "tests/test_state.py",
        "-v", "--tb=short"
    ])


def run_api_tests():
    """Run API tests."""
    return run_command([
        "poetry", "run", "pytest", 
        "tests/test_api.py",
        "-v", "--tb=short"
    ])


def run_integration_tests():
    """Run integration tests."""
    return run_command([
        "poetry", "run", "pytest", 
        "tests/test_integration.py",
        "-v", "--tb=short"
    ])


def run_pipeline_tests():
    """Run S2ST pipeline tests."""
    return run_command([
        "poetry", "run", "pytest", 
        "tests/test_s2st.py",
        "tests/test_websocket.py",
        "-v", "--tb=short"
    ])


def run_coverage():
    """Run tests with coverage report."""
    return run_command([
        "poetry", "run", "pytest", 
        "tests/",
        "--cov=src",
        "--cov-report=html",
        "--cov-report=term-missing",
        "-v"
    ])


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Voice Gateway Test Runner")
    parser.add_argument(
        "suite",
        choices=["all", "unit", "api", "integration", "pipeline", "coverage"],
        help="Test suite to run"
    )
    
    args = parser.parse_args()
    
    test_functions = {
        "all": run_all_tests,
        "unit": run_unit_tests,
        "api": run_api_tests,
        "integration": run_integration_tests,
        "pipeline": run_pipeline_tests,
        "coverage": run_coverage,
    }
    
    exit_code = test_functions[args.suite]()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
