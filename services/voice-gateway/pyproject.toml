[tool.poetry]
name = "voice-gateway"
version = "0.1.0"
description = "Cortexa Real-Time Voice Translation Service"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
websockets = "^12.0"
cortexa-common = {path = "../../packages/cortexa-common", develop = true}

# S2ST Pipeline Dependencies (optional for testing)
# faster-whisper = "^0.10.0"  # Optional - mocked in tests
# torch = "^2.1.0"  # Optional - heavy dependency
# torchaudio = "^2.1.0"  # Optional - heavy dependency
# transformers = "^4.35.0"  # Optional - heavy dependency
# sentencepiece = "^0.1.99"  # Optional - heavy dependency

# Audio Processing
numpy = "^1.24.0"
webrtcvad = "^2.0.0"
# scipy = "^1.11.0"  # Optional - heavy dependency
# librosa = "^0.10.0"  # Optional - heavy dependency
# soundfile = "^0.12.0"  # Optional - heavy dependency

# Asynchronous Task Queue (optional for testing)
# arq = "^0.25.0"  # Optional - mocked in tests
redis = "^5.0.1"  # Simplified version

# HTTP Client for service communication
httpx = "^0.25.2"

# Event streaming and messaging (optional for testing)
# aiokafka = "^0.9.0"  # Optional - mocked in tests

# Database (if needed for call metadata)
sqlmodel = "^0.0.14"
alembic = "^1.13.0"
psycopg = {extras = ["binary"], version = "^3.1.0"}

# Monitoring and logging
structlog = "^23.2.0"
prometheus-client = "^0.19.0"

# Utilities
pydantic = {extras = ["email"], version = "^2.5.0"}
pydantic-settings = "^2.1.0"
python-multipart = "^0.0.6"
setuptools = "^80.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-mock = "^3.12.0"
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.7.0"
httpx = "^0.25.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src", "cortexacommon"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "faster_whisper.*",
    "webrtcvad.*",
    "librosa.*",
    "soundfile.*",
    "arq.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
