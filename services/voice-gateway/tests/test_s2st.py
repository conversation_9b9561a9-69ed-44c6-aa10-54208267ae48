import asyncio
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
import numpy as np

from src.pipeline.s2st import (
    VADProcessor,
    STTProcessor,
    TranslationProcessor,
    TTSProcessor,
    S2STProcessor,
)
from src.pipeline.state import ConnectionState, CallState


class TestVADProcessor:
    """Test suite for Voice Activity Detection processor."""

    def test_vad_processor_initialization(self, mock_vad):
        """Test VAD processor initialization."""
        vad = VADProcessor(aggressiveness=2, frame_duration_ms=30)
        
        assert vad.aggressiveness == 2
        assert vad.frame_duration_ms == 30
        assert vad.frame_size == 480  # 30ms at 16kHz

    def test_vad_processor_no_webrtcvad(self):
        """Test VAD processor when webrtcvad is not available."""
        with patch("src.pipeline.s2st.webrtcvad", None):
            vad = VADProcessor()
            assert vad._vad is None

    def test_is_speech_with_vad(self, mock_vad):
        """Test speech detection with VAD."""
        vad = VADProcessor()
        
        # Create proper sized audio frame (480 samples * 2 bytes = 960 bytes)
        audio_frame = b'\x00' * 960
        
        mock_vad.is_speech.return_value = True
        result = vad.is_speech(audio_frame)
        
        assert result is True
        mock_vad.is_speech.assert_called_once()

    def test_is_speech_without_vad(self):
        """Test speech detection without VAD (fallback)."""
        with patch("src.pipeline.s2st.webrtcvad", None):
            vad = VADProcessor()
            audio_frame = b'\x00' * 960
            
            # Should return True when VAD is not available
            result = vad.is_speech(audio_frame)
            assert result is True

    def test_is_speech_wrong_frame_size(self, mock_vad):
        """Test speech detection with wrong frame size."""
        vad = VADProcessor()
        
        # Wrong size frame
        audio_frame = b'\x00' * 100
        
        result = vad.is_speech(audio_frame)
        assert result is False

    def test_is_speech_vad_error(self, mock_vad):
        """Test speech detection when VAD raises error."""
        vad = VADProcessor()
        audio_frame = b'\x00' * 960
        
        mock_vad.is_speech.side_effect = Exception("VAD error")
        
        result = vad.is_speech(audio_frame)
        assert result is True  # Should assume speech on error


class TestSTTProcessor:
    """Test suite for Speech-to-Text processor."""

    @pytest.mark.asyncio
    async def test_stt_processor_initialization(self, mock_whisper):
        """Test STT processor initialization."""
        stt = STTProcessor()
        
        assert stt.model is not None
        mock_whisper.assert_called_once()

    @pytest.mark.asyncio
    async def test_transcribe_success(self, mock_whisper):
        """Test successful transcription."""
        stt = STTProcessor()
        audio_data = b"fake_audio_data"
        
        # Mock transcription result
        mock_segments = [
            {"text": "Hello world", "start": 0.0, "end": 2.0}
        ]
        mock_info = {"language": "en", "language_probability": 0.95}
        mock_whisper.transcribe.return_value = (mock_segments, mock_info)
        
        text, confidence = await stt.transcribe(audio_data)
        
        assert text == "Hello world"
        assert confidence == 0.95
        mock_whisper.transcribe.assert_called_once()

    @pytest.mark.asyncio
    async def test_transcribe_multiple_segments(self, mock_whisper):
        """Test transcription with multiple segments."""
        stt = STTProcessor()
        audio_data = b"fake_audio_data"
        
        mock_segments = [
            {"text": "Hello", "start": 0.0, "end": 1.0},
            {"text": " world", "start": 1.0, "end": 2.0},
            {"text": "!", "start": 2.0, "end": 2.5}
        ]
        mock_info = {"language": "en", "language_probability": 0.90}
        mock_whisper.transcribe.return_value = (mock_segments, mock_info)
        
        text, confidence = await stt.transcribe(audio_data)
        
        assert text == "Hello world!"
        assert confidence == 0.90

    @pytest.mark.asyncio
    async def test_transcribe_empty_result(self, mock_whisper):
        """Test transcription with empty result."""
        stt = STTProcessor()
        audio_data = b"fake_audio_data"
        
        mock_segments = []
        mock_info = {"language": "en", "language_probability": 0.50}
        mock_whisper.transcribe.return_value = (mock_segments, mock_info)
        
        text, confidence = await stt.transcribe(audio_data)
        
        assert text == ""
        assert confidence == 0.50

    @pytest.mark.asyncio
    async def test_transcribe_error(self, mock_whisper):
        """Test transcription error handling."""
        stt = STTProcessor()
        audio_data = b"fake_audio_data"
        
        mock_whisper.transcribe.side_effect = Exception("Transcription failed")
        
        text, confidence = await stt.transcribe(audio_data)
        
        assert text == ""
        assert confidence == 0.0


class TestTranslationProcessor:
    """Test suite for Translation processor."""

    @pytest.mark.asyncio
    async def test_translation_processor_initialization(self, mock_translator):
        """Test translation processor initialization."""
        translator = TranslationProcessor()
        
        assert translator.pipeline is not None

    @pytest.mark.asyncio
    async def test_translate_success(self, mock_translator):
        """Test successful translation."""
        translator = TranslationProcessor()
        text = "Hello world"
        
        mock_translator.return_value = [{"translation_text": "Hola mundo"}]
        
        result = await translator.translate(text)
        
        assert result == "Hola mundo"
        mock_translator.assert_called_once_with(text)

    @pytest.mark.asyncio
    async def test_translate_empty_text(self, mock_translator):
        """Test translation with empty text."""
        translator = TranslationProcessor()
        
        result = await translator.translate("")
        
        assert result == ""
        mock_translator.assert_not_called()

    @pytest.mark.asyncio
    async def test_translate_error(self, mock_translator):
        """Test translation error handling."""
        translator = TranslationProcessor()
        text = "Hello world"
        
        mock_translator.side_effect = Exception("Translation failed")
        
        result = await translator.translate(text)
        
        assert result == text  # Should return original text on error


class TestTTSProcessor:
    """Test suite for Text-to-Speech processor."""

    @pytest.mark.asyncio
    async def test_tts_processor_initialization(self, mock_openai_tts):
        """Test TTS processor initialization."""
        tts = TTSProcessor()
        
        assert tts.client is not None
        mock_openai_tts.assert_called_once()

    @pytest.mark.asyncio
    async def test_synthesize_success(self, mock_openai_tts):
        """Test successful TTS synthesis."""
        tts = TTSProcessor()
        text = "Hello world"
        
        mock_response = MagicMock()
        mock_response.content = b"fake_tts_audio"
        mock_openai_tts.return_value.audio.speech.create.return_value = mock_response
        
        result = await tts.synthesize(text)
        
        assert result == b"fake_tts_audio"
        mock_openai_tts.return_value.audio.speech.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_synthesize_empty_text(self, mock_openai_tts):
        """Test TTS synthesis with empty text."""
        tts = TTSProcessor()
        
        result = await tts.synthesize("")
        
        assert result is None
        mock_openai_tts.return_value.audio.speech.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_synthesize_error(self, mock_openai_tts):
        """Test TTS synthesis error handling."""
        tts = TTSProcessor()
        text = "Hello world"
        
        mock_openai_tts.return_value.audio.speech.create.side_effect = Exception("TTS failed")
        
        result = await tts.synthesize(text)
        
        assert result is None


class TestS2STProcessor:
    """Test suite for main S2ST processor."""

    def test_s2st_processor_initialization(self, mock_vad, mock_whisper, mock_translator, mock_openai_tts):
        """Test S2ST processor initialization."""
        processor = S2STProcessor()
        
        assert processor.vad is not None
        assert processor.stt is not None
        assert processor.translator is not None
        assert processor.tts is not None
        assert processor.min_speech_duration == 1.0
        assert processor.max_silence_duration == 2.0
        assert isinstance(processor.speech_buffer, bytearray)
        assert processor.last_speech_time == 0.0
        assert processor.is_in_speech is False

    @pytest.mark.asyncio
    async def test_process_audio_chunk_no_audio(self, connection_state):
        """Test processing when no audio is available."""
        processor = S2STProcessor()
        
        # Empty inbound queue
        await processor.process_audio_chunk(connection_state)
        
        # Should not raise any errors

    @pytest.mark.asyncio
    async def test_process_complete_segment(self, mock_vad, mock_whisper, mock_translator, mock_openai_tts, connection_state):
        """Test processing a complete speech segment."""
        processor = S2STProcessor()
        
        # Mock all components
        mock_vad.is_speech.return_value = True
        mock_whisper.transcribe.return_value = (
            [{"text": "Hello world", "start": 0.0, "end": 2.0}],
            {"language": "en", "language_probability": 0.95}
        )
        mock_translator.return_value = [{"translation_text": "Hola mundo"}]
        mock_response = MagicMock()
        mock_response.content = b"fake_tts_audio"
        mock_openai_tts.return_value.audio.speech.create.return_value = mock_response
        
        # Add audio to inbound queue
        audio_chunk = b'\x00' * 960  # Proper frame size
        await connection_state.inbound_queue.put(audio_chunk)
        
        # Process the segment
        await processor._process_complete_segment(connection_state)
        
        # Check that TTS audio was added to outbound queue
        assert not connection_state.outbound_queue.empty()
        tts_audio = await connection_state.outbound_queue.get()
        assert tts_audio == b"fake_tts_audio"
