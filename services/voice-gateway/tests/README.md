# Voice Gateway Tests

This directory contains comprehensive tests for the Voice Gateway service, covering all major components and functionality.

## Test Structure

### Test Files

- **`conftest.py`** - Pytest fixtures and test configuration
- **`test_config.py`** - Configuration and settings tests
- **`test_security.py`** - Authentication and security tests
- **`test_state.py`** - Connection state management tests
- **`test_s2st.py`** - Speech-to-Speech Translation pipeline tests
- **`test_api.py`** - HTTP API endpoint tests
- **`test_websocket.py`** - WebSocket endpoint and pipeline orchestration tests
- **`test_integration.py`** - End-to-end integration tests with real audio samples

### Audio Samples

The `audio_samples/` directory contains real audio files for testing:

- **`silence_1s.wav`** - 1 second of silence for VAD testing
- **`speech_5s.wav`** - 5 seconds of speech: "I've got a drunk driver in front of me - I'm going to pull him over and see what's up"

## Running Tests

### Prerequisites

```bash
# Install dependencies
poetry install

# Ensure you're in the voice-gateway directory
cd services/voice-gateway
```

### Test Suites

#### Run All Tests
```bash
poetry run pytest tests/ -v
```

#### Run Specific Test Suites
```bash
# Configuration tests
poetry run pytest tests/test_config.py -v

# Security and authentication tests
poetry run pytest tests/test_security.py -v

# State management tests
poetry run pytest tests/test_state.py -v

# S2ST pipeline tests
poetry run pytest tests/test_s2st.py -v

# API endpoint tests
poetry run pytest tests/test_api.py -v

# WebSocket tests
poetry run pytest tests/test_websocket.py -v

# Integration tests
poetry run pytest tests/test_integration.py -v
```

#### Using the Test Runner Script
```bash
# Run all tests
python run_tests.py all

# Run unit tests only
python run_tests.py unit

# Run API tests
python run_tests.py api

# Run integration tests
python run_tests.py integration

# Run pipeline tests
python run_tests.py pipeline

# Run with coverage
python run_tests.py coverage
```

## Test Coverage

The tests cover the following areas:

### Core Configuration (`test_config.py`)
- Default configuration values
- Environment variable overrides
- Validation rules (VAD aggressiveness, model sizes, etc.)
- Configuration serialization and deserialization

### Security & Authentication (`test_security.py`)
- JWT token validation for WebSocket connections
- User permission verification
- WebSocket connection manager functionality
- Authentication error handling

### State Management (`test_state.py`)
- Connection state lifecycle
- Audio buffer management
- Transcript and error tracking
- Connection statistics
- State cleanup and resource management

### S2ST Pipeline (`test_s2st.py`)
- Voice Activity Detection (VAD) processor
- Speech-to-Text (STT) processor
- Translation processor
- Text-to-Speech (TTS) processor
- Complete S2ST pipeline integration

### HTTP API (`test_api.py`)
- Root endpoint service information
- Health check endpoints
- Metrics and statistics endpoints
- Error handling and CORS configuration
- API documentation endpoints

### WebSocket & Pipeline (`test_websocket.py`)
- WebSocket connection establishment
- Message handling (audio and control messages)
- Pipeline task orchestration (reader, writer, processor, heartbeat)
- Error recovery and cleanup

### Integration Tests (`test_integration.py`)
- End-to-end voice translation workflows
- Real audio sample processing
- Performance and memory usage tests
- Concurrent connection handling
- Error recovery scenarios

## Test Fixtures

The `conftest.py` file provides comprehensive fixtures:

### Core Fixtures
- `test_settings` - Test configuration with safe defaults
- `mock_settings` - Mocked global settings
- `test_client` - FastAPI test client
- `mock_user` - Authenticated user token data
- `mock_websocket` - Mock WebSocket connection

### Audio Fixtures
- `audio_samples_dir` - Path to audio samples
- `silence_audio` - 1-second silence audio data
- `speech_audio` - 5-second speech audio data

### Component Mocks
- `mock_vad` - Mock WebRTC VAD processor
- `mock_whisper` - Mock Faster Whisper STT
- `mock_translator` - Mock translation pipeline
- `mock_openai_tts` - Mock OpenAI TTS client
- `mock_httpx_client` - Mock HTTP client
- `mock_redis` - Mock Redis client
- `mock_kafka` - Mock Kafka producer

### Utility Functions
- `create_test_audio_chunk()` - Generate test audio data
- `create_silence_chunk()` - Generate silent audio data

## Test Philosophy

### Mocking Strategy
- External services (OpenAI, translation models) are mocked to ensure tests are fast and reliable
- Real audio samples are used for integration testing
- Network dependencies are mocked to avoid external service calls

### Test Isolation
- Each test is independent and can run in isolation
- Fixtures provide clean state for each test
- No shared state between tests

### Error Testing
- Comprehensive error handling tests
- Edge cases and boundary conditions
- Recovery scenarios and graceful degradation

## Continuous Integration

These tests are designed to run in CI/CD environments:

- No external dependencies required (all mocked)
- Fast execution (most tests complete in milliseconds)
- Comprehensive coverage of critical paths
- Clear failure messages and debugging information

## Adding New Tests

When adding new functionality:

1. **Add unit tests** for individual components
2. **Add integration tests** for end-to-end workflows
3. **Update fixtures** if new mocking is needed
4. **Add error cases** for robust error handling
5. **Update this README** with new test descriptions

### Test Naming Convention
- Test files: `test_<component>.py`
- Test classes: `Test<ComponentName>`
- Test methods: `test_<functionality>_<scenario>`

### Example Test Structure
```python
class TestNewComponent:
    """Test suite for new component."""

    def test_initialization(self):
        """Test component initialization."""
        pass

    def test_normal_operation(self):
        """Test normal operation scenario."""
        pass

    def test_error_handling(self):
        """Test error handling."""
        pass

    @pytest.mark.asyncio
    async def test_async_operation(self):
        """Test async operation."""
        pass
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running tests from the voice-gateway directory
2. **Missing Audio Files**: Audio samples should be in `tests/audio_samples/`
3. **Async Test Issues**: Use `@pytest.mark.asyncio` for async tests
4. **Mock Issues**: Check that mock paths match the actual import paths

### Debug Mode
```bash
# Run with verbose output and no capture
poetry run pytest tests/ -v -s

# Run specific test with debugging
poetry run pytest tests/test_integration.py::TestClass::test_method -v -s --tb=long
```
