import asyncio
import os
import pytest
import tempfile
from pathlib import Path
from typing import As<PERSON><PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock, patch

import httpx
from fastapi.testclient import TestClient
from fastapi import WebSocket

from cortexacommon.security.auth import TokenData
from src.main import app
from src.core.config import VoiceGatewaySettings
from src.pipeline.state import ConnectionState, CallState


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings() -> VoiceGatewaySettings:
    """Create test settings with safe defaults."""
    return VoiceGatewaySettings(
        service_name="voice-gateway-test",
        port=8002,
        debug=True,
        # S2ST Pipeline - use lightweight settings for testing
        whisper_model_size="tiny",
        whisper_compute_type="int8",
        whisper_device="cpu",
        vad_aggressiveness=1,
        vad_frame_duration_ms=30,
        translation_model="Helsinki-NLP/opus-mt-en-es",
        translation_device="cpu",
        tts_provider="openai",
        tts_model="tts-1",
        tts_voice="alloy",
        tts_api_key="test-api-key",
        # Audio settings
        audio_sample_rate=16000,
        audio_chunk_size=1024,
        audio_channels=1,
        # WebSocket settings
        ws_max_connections=10,
        ws_heartbeat_interval=30,
        ws_connection_timeout=300,
        # External services - use test URLs
        call_data_service_url="http://test-call-data:8003",
        auth_service_url="http://test-auth:8001",
        arq_redis_settings="redis://test-redis:6379/1",
        arq_max_jobs=5,
        arq_job_timeout=60,
    )


@pytest.fixture
def mock_settings(test_settings: VoiceGatewaySettings):
    """Mock the global settings with test settings."""
    with patch("src.core.config.settings", test_settings):
        yield test_settings


@pytest.fixture
def test_client(mock_settings: VoiceGatewaySettings) -> TestClient:
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_user() -> TokenData:
    """Create a mock authenticated user."""
    import uuid
    return TokenData(
        user_id=str(uuid.uuid4()),
        username="testuser",
        email="<EMAIL>",
        roles=["user"],
        permissions=["voice:translate"],
        exp=9999999999,  # Far future expiration
    )


@pytest.fixture
def mock_websocket() -> MagicMock:
    """Create a mock WebSocket connection."""
    websocket = MagicMock(spec=WebSocket)
    websocket.accept = AsyncMock()
    websocket.close = AsyncMock()
    websocket.send_json = AsyncMock()
    websocket.send_bytes = AsyncMock()
    websocket.receive_json = AsyncMock()
    websocket.receive_bytes = AsyncMock()
    websocket.receive_text = AsyncMock()
    return websocket


@pytest.fixture
async def connection_state(mock_websocket: MagicMock, mock_user: TokenData) -> ConnectionState:
    """Create a test connection state."""
    return ConnectionState(
        websocket=mock_websocket,
        user=mock_user,
        call_id="test-call-123",
        state=CallState.ACTIVE,
    )


@pytest.fixture
def audio_samples_dir() -> Path:
    """Get the path to audio samples directory."""
    return Path(__file__).parent / "audio_samples"


@pytest.fixture
def silence_audio(audio_samples_dir: Path) -> bytes:
    """Load the 1-second silence audio sample."""
    silence_path = audio_samples_dir / "silence_1s.wav"
    if not silence_path.exists():
        pytest.skip(f"Audio sample not found: {silence_path}")
    return silence_path.read_bytes()


@pytest.fixture
def speech_audio(audio_samples_dir: Path) -> bytes:
    """Load the 5-second speech audio sample."""
    speech_path = audio_samples_dir / "speech_5s.wav"
    if not speech_path.exists():
        pytest.skip(f"Audio sample not found: {speech_path}")
    return speech_path.read_bytes()


@pytest.fixture
def mock_vad():
    """Mock WebRTC VAD processor."""
    with patch("webrtcvad.Vad") as mock_vad_class:
        mock_vad_instance = MagicMock()
        mock_vad_instance.is_speech.return_value = True
        mock_vad_class.return_value = mock_vad_instance
        yield mock_vad_instance


@pytest.fixture
def mock_whisper():
    """Mock Faster Whisper STT processor."""
    with patch("faster_whisper.WhisperModel") as mock_whisper_class:
        mock_whisper_instance = MagicMock()
        mock_whisper_instance.transcribe.return_value = (
            [{"text": "Hello world", "start": 0.0, "end": 2.0}],
            {"language": "en", "language_probability": 0.95}
        )
        mock_whisper_class.return_value = mock_whisper_instance
        yield mock_whisper_instance


@pytest.fixture
def mock_translator():
    """Mock translation pipeline."""
    with patch("transformers.pipeline") as mock_pipeline_func:
        mock_translator = MagicMock()
        mock_translator.return_value = [{"translation_text": "Hola mundo"}]
        mock_pipeline_func.return_value = mock_translator
        yield mock_translator


@pytest.fixture
def mock_openai_tts():
    """Mock OpenAI TTS client."""
    with patch("openai.OpenAI") as mock_openai_class:
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_response.content = b"fake_tts_audio_data"
        mock_client.audio.speech.create.return_value = mock_response
        mock_openai_class.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_httpx_client():
    """Mock HTTPX client for external service calls."""
    with patch("httpx.AsyncClient") as mock_client_class:
        mock_client = AsyncMock()
        mock_client.get.return_value.status_code = 200
        mock_client.get.return_value.json.return_value = {"status": "ok"}
        mock_client.post.return_value.status_code = 200
        mock_client.post.return_value.json.return_value = {"id": "test-id"}
        mock_client_class.return_value.__aenter__.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    with patch("redis.Redis") as mock_redis_class:
        mock_redis_instance = MagicMock()
        mock_redis_instance.ping.return_value = True
        mock_redis_instance.set.return_value = True
        mock_redis_instance.get.return_value = b'{"test": "data"}'
        mock_redis_class.return_value = mock_redis_instance
        yield mock_redis_instance


@pytest.fixture
def mock_kafka():
    """Mock Kafka producer."""
    with patch("aiokafka.AIOKafkaProducer") as mock_producer_class:
        mock_producer = AsyncMock()
        mock_producer.start = AsyncMock()
        mock_producer.stop = AsyncMock()
        mock_producer.send_and_wait = AsyncMock()
        mock_producer_class.return_value = mock_producer
        yield mock_producer


# Audio processing utilities for tests
def create_test_audio_chunk(duration_ms: int = 100, sample_rate: int = 16000) -> bytes:
    """Create a test audio chunk with specified duration."""
    import numpy as np
    
    samples = int(duration_ms * sample_rate / 1000)
    # Generate simple sine wave
    t = np.linspace(0, duration_ms / 1000, samples, False)
    audio = np.sin(2 * np.pi * 440 * t)  # 440 Hz tone
    # Convert to 16-bit PCM
    audio_int16 = (audio * 32767).astype(np.int16)
    return audio_int16.tobytes()


def create_silence_chunk(duration_ms: int = 100, sample_rate: int = 16000) -> bytes:
    """Create a silent audio chunk with specified duration."""
    import numpy as np
    
    samples = int(duration_ms * sample_rate / 1000)
    audio = np.zeros(samples, dtype=np.int16)
    return audio.tobytes()
